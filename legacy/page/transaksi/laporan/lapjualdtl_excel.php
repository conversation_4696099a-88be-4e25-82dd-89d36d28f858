<?php

use App\Models\BarangBundling;
use App\Models\JualDetail;
use App\Models\JurnalDetail;
use App\Services\ExcelReportBuilder;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Shared\Date;

$awal = $_GET['from'];
$akhir = $_GET['to'];
$channel = $_GET['channel'] ?? null;
$brg = $_GET['brg'];
$cust = $_GET['cust'];

$jfrom = date('d-m-Y', strtotime($awal));
$jto = date('d-m-Y', strtotime($akhir));

// Replace temporary table and raw SQL with Eloquent query
$items = JualDetail::query()
    ->with(['jual.customer.jenis_customer', 'barang.kategoriBarang'])
    ->when($cust, function ($query, $cust) {
        return $query->whereHas('jual', function ($query) use ($cust) {
            $query->where('kdcust', $cust);
        });
    })
    ->when($brg, function ($query, $brg) {
        return $query->where('kdproduk', $brg);
    })
    ->when($channel, function ($query, $channel) {
        return $query->whereHas('jual', function ($query) use ($channel) {
            $query->where('channel', $channel);
        });
    })
    ->whereBetween(DB::raw('DATE(tgljual)'), [$awal, $akhir])
    ->orderBy('nojualdtl')
    ->get();

$excelReport = ExcelReportBuilder::make()
    ->addHeading('LAPORAN DETAIL PENJUALAN')
    ->addHeading("PERIODE : {$jfrom} s/d {$jto}")
    ->setColumns([
        'NO',
        'ORDER',
        'TRANSAKSI',
        'TANGGAL',
        'KANAL',
        'KODE CUSTOMER',
        'CUSTOMER',
        'KODE',
        'NAMA PRODUK',
        'HARGA',
        'HARGA REGULER',
        'DISKON (%)',
        'DISKON (RP)',
        'FLASH (%)',
        'FLASH (RP)',
        'HARGA DISKON',
        'QTY',
        'PENJUALAN BERSIH',
        'PENJUALAN KOTOR',
        'BEBAN DISKON REGULER',
        'TOTAL HPP',
        'TOTAL ROYALTI',
        'TOTAL HPP + ROYALTI',
        'LABA KOTOR',
        'JENIS CUSTOMER',
        'JENIS PRODUK',
        'KATEGORI PRODUK',
    ])
    ->setColumnFormat('D', 'yyyy-mm-dd')
    ->setColumnFormat('J:K', '#,##0')
    ->setColumnFormat('L', '0%')
    ->setColumnFormat('M', '#,##0')
    ->setColumnFormat('N', '0%')
    ->setColumnFormat('O:R', '#,##0')
    ->setColumnFormat('S:X', '#,##0');

$i = 1;
$xtotal = 0;
$xtotqty = 0;

foreach ($items as $d) {
    $diskon = ($d->disc / 100) * $d->hrgsat;
    $flash = ($d->flash / 100) * ($d->hrgsat - $diskon);
    if ($d->barang->is_bundling) {
        $kdBarangs = BarangBundling::query()
            ->where('kd_bundling', $d->barang->barang_kd)
            ->pluck('barang');
        $totalHPP = JurnalDetail::query()
            ->where('notran_jurnaldtl', $d->nojualdtl)
            ->where('rekening', '51101')
            ->whereIn('kdcssp', $kdBarangs)
            ->sum('nominal');
        $totalRoyalti = JurnalDetail::query()
            ->where('notran_jurnaldtl', $d->nojualdtl)
            ->where('rekening', '52116')
            ->whereIn('kdcssp', $kdBarangs)
            ->sum('nominal');
    } else {
        $totalHPP = JurnalDetail::query()
            ->where('notran_jurnaldtl', $d->nojualdtl)
            ->where('rekening', '51101')
            ->where('kdcssp', $d->barang)
            ->sum('nominal');
        $totalRoyalti = JurnalDetail::query()
            ->where('notran_jurnaldtl', $d->nojualdtl)
            ->where('rekening', '52116')
            ->whereIn('kdcssp', $kdBarangs)
            ->sum('nominal');
    }
    $excelReport->addRow([
        $i++,
        $d->jual->norder,
        $d->nojualdtl,
        Date::dateTimeToExcel($d->tgljual),
        $d->jual->channel?->getLabel() ?? '',
        $d->jual->kdcust,
        $d->jual->customer?->nama_cust ?? '',
        $d->kdproduk,
        $d->barang?->nama_barang ?? '',
        $d->barang?->reguler ?? 0,
        $d->hrgsat,
        $d->disc / 100,
        $diskon,
        $d->flash / 100,
        $flash,
        $d->hrgsat - $diskon,
        $d->qty,
        $d->sharga,
        $d->qty * $d->hrgsat,
        $d->qty * $diskon,
        $totalHPP,
        $totalRoyalti,
        $totalHPP + $totalRoyalti,
        $d->sharga - ($totalHPP + $totalRoyalti),
        $d->jual->customer?->jenis_customer?->nama_jcust ?? '',
        $d->barang?->kategori ?? '',
        $d->barang?->kategoriBarang?->nama_katproduk ?? '',
    ]);

    $xtotal += $d->sharga;
    $xtotqty += $d->qty;
}

$excelReport->footer([
    'TOTAL',
    ...array_fill(0, 15, ''),
    $xtotqty,
    $xtotal,
]);

$excelReport->download("Laporan Detail Penjualan ({$jfrom} - {$jto})");
